/* 全局样式 */
html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: white;
    color: #333;
    font-size: 12px; /* 进一步减小字体大小 */
    line-height: 1.4;
    width: 100%;
    min-height: 100vh;
    overflow-x: hidden !important;
}

.container {
    max-width: 100%; /* 修改为100%宽度 */
    margin: 0; /* 移除左右边距 */
    padding: 4px; /* 减小内边距 */
    min-height: calc(100vh - 36px); /* 自适应浏览器高度，减去导航栏高度 */
    box-sizing: border-box;
}

/* 导航栏样式 */
.navbar {
    background-color: #1976d2;
    color: white;
    padding: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 36px; /* 进一步减小导航栏高度 */
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    left: 0;
    right: 0;
}

.navbar .container {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    padding: 0 8px;
    height: 100%;
    width: 100%;
    max-width: 100%;
    margin: 0;
}

/* 导航栏左侧元素 */
.navbar-left {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex: 1 !important;
}

.navbar-brand {
    font-size: 1.1rem;
    font-weight: bold;
    color: white;
    padding: 0;
    display: flex !important;
    align-items: center !important;
    text-decoration: none;
    margin-right: 10px !important; /* 添加右侧间距 */
    order: -3 !important; /* 确保LOGO在最左侧 */
    float: left !important;
}

.logo-img {
    height: 22px; /* 减小logo大小 */
    width: 22px;
    border-radius: 50%;
    background-color: #0d47a1;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px; /* 减小字体大小 */
    margin-left: 2px;
    border: 1px solid white; /* 减小边框 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.navbar-nav {
    display: flex !important;
    list-style: none;
    margin: 0;
    padding: 0;
    height: 100%;
    margin-right: 15px !important; /* 添加右侧间距 */
    order: -2 !important; /* 确保"物料检验"在LOGO之后 */
    float: left !important;
}

.nav-item {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0 10px; /* 减小内边距 */
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 12px; /* 减小字体大小 */
}

.nav-link:hover {
    background-color: #1565c0;
}

/* 导航栏右侧元素 */
.navbar-right {
    display: flex;
    align-items: center;
}

.nav-icon {
    color: white;
    font-size: 14px; /* 减小图标大小 */
    margin-left: 3px;
    cursor: pointer;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.nav-icon:hover {
    background-color: rgba(255, 255, 255, 0.2);
}


/* 浮动搜索面板 - 修复点击问题 */
.floating-panel {
    position: fixed;
    top: 0;
    left: -450px; /* 初始隐藏在左侧 */
    width: 400px;
    height: 100vh;
    background-color: white;
    border-radius: 0 8px 8px 0;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
    z-index: 100; /* 降低z-index */
    transition: left 0.35s cubic-bezier(0.16, 1, 0.3, 1);
    display: flex;
    flex-direction: column;
    pointer-events: none; /* 隐藏时不阻止点击 */
}

.floating-panel.active {
    left: 0;
    pointer-events: auto; /* 显示时恢复点击 */
}/* 初始隐藏在左侧 */
    width: 400px;
    height: 100vh;
    max-height: 100vh;
    background-color: white;
    border-radius: 0 8px 8px 0;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
    z-index: 100;
    transition: left 0.35s cubic-bezier(0.16, 1, 0.3, 1); /* 优化过渡效果，使用 ease-out-expo 曲线 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    will-change: transform; /* 提示浏览器优化动画性能 */
    transform: translateZ(0); /* 强制硬件加速 */
}

.floating-panel.visible, .floating-panel.active {
    left: 0; /* 激活时显示 */
}

/* 添加遮罩层 - 移除灰色背景 */
.floating-panel.active::before {
    display: none; /* 完全移除遮罩层 */
}

.panel-header {
    padding: 15px;
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.close-panel {
    font-size: 20px;
    cursor: pointer;
    color: #6c757d;
}

.close-panel:hover {
    color: #343a40;
}

.panel-body {
    padding: 0;
    overflow-y: auto;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
}

/* 面板标签样式 */
.panel-tabs {
    display: flex;
    background-color: #f1f3f5;
    border-bottom: 1px solid #dee2e6;
    flex-wrap: wrap; /* 允许标签换行 */
}

.panel-tab {
    flex: 1;
    padding: 8px 5px;
    text-align: center;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    position: relative;
    font-size: 12px;
    min-width: 50px;
    transition: all 0.2s ease;
    border-right: 1px dashed #ccc; /* 添加右侧虚线边框 */
}

/* 最后一个panel-tab不需要右侧边框 */
.panel-tab:last-child {
    border-right: none;
}

/* 菜单表格布局样式 */
.menu-grid {
    display: table;
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    table-layout: fixed; /* 添加固定表格布局以确保列宽一致 */
}

/* 强制四等分布局 - 确保每列严格25%宽度 */
.menu-grid .menu-row {
    display: table-row;
    width: 100%;
}

.menu-grid .menu-cell {
    display: table-cell;
    width: 25% !important; /* 强制每列25%宽度 */
    max-width: 25% !important;
    min-width: 25% !important;
    box-sizing: border-box;
    overflow: hidden; /* 防止内容溢出 */
    white-space: nowrap; /* 防止文字换行影响布局 */
    text-overflow: ellipsis; /* 超长文字显示省略号 */
}

/* 纵向布局方案 - 作为对比选项 */
.menu-grid.vertical-layout {
    display: flex;
    flex-direction: column;
    gap: 0;
    table-layout: auto;
}

.menu-grid.vertical-layout .menu-row {
    display: flex;
    flex-direction: column;
    border-bottom: none;
}

.menu-grid.vertical-layout .menu-cell {
    display: flex;
    width: 100% !important;
    padding: 12px 20px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    margin-left: 0 !important;
}

.menu-grid.vertical-layout .menu-cell:first-child {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
}

.menu-grid.vertical-layout .menu-cell:last-child {
    border-bottom: none;
}

.menu-grid.vertical-layout .menu-cell .menu-item {
    width: 100%;
    justify-content: flex-start;
    padding: 8px 0;
}

/* 基础菜单行样式 - 被.menu-grid .menu-row覆盖 */
.menu-row {
    display: table-row;
    border-bottom: 1px solid #e0e0e0; /* 水平分隔线 */
}

.menu-row:last-child {
    border-bottom: none;
}

/* 基础菜单单元格样式 - 被.menu-grid .menu-cell覆盖 */
.menu-cell {
    padding: 10px 8px; /* 调整内边距 */
    text-align: center;
    vertical-align: middle;
    position: relative; /* 添加相对定位以便精确控制边框 */
}

.menu-cell:first-child {
    width: 25% !important; /* 强制第一列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    padding-right: 8px; /* 增加右侧内边距，改善视觉间距 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.menu-cell:nth-child(2) {
    width: 25% !important; /* 强制第二列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    padding-left: 8px; /* 增加左侧内边距，改善视觉间距 */
    padding-right: 8px; /* 增加右侧内边距 */
    text-align: center; /* 文本居中对齐，更美观 */
    border-right: none; /* 取消检验记录单元格右侧的虚线边框 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.menu-cell:nth-child(3) {
    width: 25% !important; /* 强制第三列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    padding-left: 8px; /* 左侧内边距 */
    padding-right: 8px; /* 右侧内边距 */
    text-align: center; /* 文本居中对齐 */
    border-right: none; /* 取消边框 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.menu-cell:last-child {
    width: 25% !important; /* 强制最后一列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    border-right: none;
    padding-left: 8px; /* 增加左侧内边距，改善视觉间距 */
    text-align: center; /* 文本居中对齐，更美观 */
    margin-left: 0; /* 移除负边距，避免布局问题 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

/* 使用伪元素精确控制垂直线的位置 */
.menu-cell:first-child::after {
    content: '';
    position: absolute;
    top: 25%; /* 调整垂直线位置 */
    bottom: 25%; /* 调整垂直线位置 */
    right: 0; /* 将垂直线移到右边缘 */
    width: 1px;
    background-color: #e0e0e0; /* 使用更柔和的颜色 */
    z-index: 1;
}

/* 为第二列和第三列也添加分隔线，实现均匀分割 */
.menu-cell:nth-child(2)::after,
.menu-cell:nth-child(3)::after {
    content: '';
    position: absolute;
    top: 25%;
    bottom: 25%;
    right: 0;
    width: 1px;
    background-color: #e0e0e0;
    z-index: 1;
}

.menu-item {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #212529;
    text-decoration: none;
    font-size: 12px;
    white-space: nowrap;
}

/* 二级标题菜单项样式 */
.menu-item.heading-2 {
    font-weight: 600;
    font-size: 14px;
    justify-content: flex-start;
    padding-left: 5px;
}

.menu-item i {
    margin-right: 5px;
    font-size: 14px;
    color: #6c757d;
}

.menu-item:hover {
    color: #007bff;
}

.menu-item:hover i {
    color: #007bff;
}

.panel-tab i {
    margin-right: 3px;
    font-size: 14px;
    color: #6c757d;
}

.panel-tab.active {
    color: #007bff;
    background-color: white;
}

.panel-tab.active i {
    color: #007bff;
}

.panel-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #007bff;
}

.panel-tab:hover {
    color: #007bff;
    background-color: rgba(255, 255, 255, 0.5);
}

.panel-tab:hover i {
    color: #007bff;
}

/* 面板内容样式 */
.panel-content {
    flex-grow: 1;
    overflow-y: auto;
    background-color: white;
}

.panel-tab-content {
    display: none;
    padding: 15px;
    background-color: white;
}

.panel-tab-content.active {
    display: block;
}

/* 可视化图表卡片 */
.visualization-charts {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background-color: white;
}

.chart-card {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: #495057;
}

.chart-placeholder {
    height: 180px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    border-radius: 4px;
    font-size: 12px;
}

/* 数据分析样式 */
.analytics-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.summary-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.stat-card {
    flex: 1;
    min-width: calc(50% - 10px);
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: #495057;
}

.data-list {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-list h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: #495057;
}

/* 新增检验表单 */
.quick-form-container {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quick-form-container h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 14px;
    color: #495057;
}

.form-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

/* 全检验概览样式 */
.full-inspection-summary {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.full-inspection-summary h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 14px;
    color: #495057;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    min-width: 80px;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #007bff;
}

.recent-list {
    margin-top: 15px;
}

.recent-list h5 {
    font-size: 13px;
    margin-bottom: 10px;
    color: #495057;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
}

/* 搜索结果区域 */
.search-results {
    margin-top: 15px;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
}

/* 卡片容器样式 */
.float-card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.float-card {
    width: calc(50% - 10px);
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.float-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.float-card-image {
    height: 120px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.float-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.float-card-content {
    padding: 10px;
}

.float-card-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.float-card-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.float-card-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
}

.float-status-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.float-status-pass {
    background-color: #e6f7e6;
    color: #2e7d32;
}

.float-status-fail {
    background-color: #fdecea;
    color: #c62828;
}

.float-status-warning {
    background-color: #fff8e1;
    color: #ff8f00;
}

/* 下拉菜单样式 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 50;
    min-width: 180px;
    padding: 4px;
    margin: 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 3px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    display: none;
}

.dropdown-menu.multi-column {
    display: none; /* 默认隐藏 */
    width: 360px; /* 减小总宽度，从460px改为360px */
    padding: 6px;
    background-color: white;
}

.dropdown-column {
    flex: 1;
    padding: 0 6px;
    border-right: 1px dashed #ccc; /* 将实线改为虚线边框 */
}

.dropdown-column:first-child {
    flex: 2;
}

.dropdown-column:nth-child(2) {
    flex: 2;
}

.dropdown-column:last-child {
    flex: 1;
    border-right: none;
}

.dropdown-column h5 {
    font-size: 14px;
    color: #6c757d;
    margin: 8px 0;
    padding: 0 8px;
    font-weight: 600;
    text-transform: uppercase;
}

.dropdown-item-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2px;
}

.dropdown-item-group .dropdown-item {
    flex: 1;
    margin-right: 2px;
    margin-bottom: 0;
}

.dropdown-item-group .dropdown-item:last-child {
    margin-right: 0;
}

.dropdown-item {
    display: block;
    padding: 6px 10px;
    color: #212529;
    text-decoration: none;
    font-size: 12px;
    white-space: nowrap;
    border-radius: 3px;
    margin-bottom: 2px;
}

.dropdown-item i {
    margin-right: 5px;
    width: 16px;
    text-align: center;
    color: #6c757d;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.dropdown-item:hover i {
    color: #007bff;
}

/* 添加悬停时显示下拉菜单的样式 */
.nav-item:hover .dropdown-menu {
    display: block;
}

/* 简洁菜单样式 */
.dropdown-menu.simple-menu {
    min-width: 200px;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
}

.dropdown-menu.simple-menu .dropdown-header {
    padding: 12px 16px 8px 16px;
    background-color: #f8f9fa;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.dropdown-menu.simple-menu .dropdown-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.dropdown-menu.simple-menu .header-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0 0 0;
}

.dropdown-menu.simple-menu .menu-items {
    padding: 4px 0;
}

.dropdown-menu.simple-menu .menu-category {
    padding: 6px 12px 4px 12px;
    margin: 8px 6px 2px 6px;
    background-color: #e3f2fd;
    border-radius: 3px;
}

.dropdown-menu.simple-menu .menu-category:first-child {
    margin-top: 2px;
}

.dropdown-menu.simple-menu .category-title {
    font-size: 12px;
    font-weight: 700;
    color: #1976d2;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    display: block;
    text-align: center;
}

.dropdown-menu.simple-menu .menu-item {
    display: block;
    padding: 6px 12px;
    color: #333;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
    border: none;
    background: none;
    margin-left: 6px;
    border-left: 2px solid transparent;
}

.dropdown-menu.simple-menu .menu-item:hover {
    background-color: #f0f7ff;
    color: #1976d2;
    border-left-color: #1976d2;
    margin-left: 4px;
}

.dropdown-menu.simple-menu .menu-item i {
    width: 14px;
    margin-right: 6px;
    font-size: 10px;
    color: #666;
}

.dropdown-menu.simple-menu .menu-item:hover i {
    color: #1976d2;
}

/* 物料确认面板样式 */
.confirmation-container {
    padding: 20px;
}

.confirmation-header {
    text-align: center;
    margin-bottom: 20px;
}

.confirmation-header h4 {
    color: #333;
    margin-bottom: 8px;
    font-size: 18px;
}

.confirmation-header p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.confirmation-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    justify-content: space-around;
}

.confirmation-stats .stat-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    flex: 1;
    border: 1px solid #e9ecef;
}

.confirmation-stats .stat-card h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.confirmation-stats .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #1976d2;
    margin-bottom: 4px;
}

.confirmation-stats .stat-label {
    font-size: 11px;
    color: #999;
}

.confirmation-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.confirmation-actions .btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.confirmation-actions .btn-primary {
    background-color: #1976d2;
    color: white;
}

.confirmation-actions .btn-primary:hover {
    background-color: #1565c0;
}

.confirmation-actions .btn-success {
    background-color: #28a745;
    color: white;
}

.confirmation-actions .btn-success:hover {
    background-color: #218838;
}

/* 确保所有菜单行都可见 */
.floating-panel .menu-row {
    display: table-row !important;
    visibility: visible !important;
}

.floating-panel .menu-cell {
    display: table-cell !important;
    visibility: visible !important;
}

/* 特别针对物料确认行的调试样式 */
.floating-panel .menu-row:nth-child(2) {
    background-color: #ffe6e6 !important;
    border: 2px solid red !important;
    height: 50px !important;
    min-height: 50px !important;
}

.floating-panel .menu-row:nth-child(2) .menu-cell {
    background-color: #ffcccc !important;
    border: 1px solid blue !important;
    padding: 10px !important;
}

.floating-panel .menu-row:nth-child(2) .menu-item {
    color: red !important;
    font-weight: bold !important;
    font-size: 14px !important;
}

/* 简化菜单容器样式 */
.simple-menu-container {
    padding: 10px;
}

.menu-section {
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #fff;
}

.menu-section-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
}

.menu-section-row .menu-item {
    flex: 1;
    padding: 12px 8px;
    text-align: center;
    border-right: 1px solid #e0e0e0;
    text-decoration: none;
    color: #333;
    font-size: 12px;
    transition: all 0.2s ease;
    min-width: 0;
}

.menu-section-row .menu-item:last-child {
    border-right: none;
}

.menu-section-row .menu-item.heading-2 {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #1976d2;
}

.menu-section-row .menu-item:hover {
    background-color: #f0f7ff;
    color: #1976d2;
}

.menu-section-row .menu-item i {
    margin-right: 4px;
    font-size: 11px;
}

/* 全新的浮动窗口菜单样式 */
.new-menu-container {
    padding: 15px;
    background-color: #f8f9fa;
    height: 100%;
    overflow-y: auto;
}

.menu-group {
    margin-bottom: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.group-title {
    background-color: #1976d2;
    color: white;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    margin: 0;
}

.group-items {
    padding: 8px 0;
}

.group-items .nav-item {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    font-size: 13px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
}

.group-items .nav-item:last-child {
    border-bottom: none;
}

.group-items .nav-item:hover {
    background-color: #f0f7ff;
    color: #1976d2;
    padding-left: 24px;
}

.group-items .nav-item i {
    width: 16px;
    margin-right: 8px;
    color: #666;
    font-size: 12px;
}

.group-items .nav-item:hover i {
    color: #1976d2;
}

/* 表格样式 */
.table-container {
    background-color: white;
    border-radius: 3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-top: 6px;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px; /* 减小字体大小 */
}

thead {
    background-color: #f5f5f5;
}

th, td {
    padding: 4px 6px; /* 进一步减小内边距 */
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    font-weight: 600;
    color: #555;
    font-size: 12px;
}

tbody tr:hover {
    background-color: #f9f9f9;
}

/* 表单元素样式统一 */
input[type="text"], 
input[type="date"], 
input[type="number"], 
input[type="password"],
input[type="email"],
select, 
textarea,
.form-control {
    height: 32px;
    padding: 2px 8px;
    font-size: 12px;
    width: 100%;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #ddd;
    outline: none;
}

textarea {
    height: auto;
    min-height: 32px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0 12px; /* 减小内边距 */
    font-size: 12px; /* 减小字体大小 */
    line-height: 30px; /* 调整行高 */
    height: 32px; /* 设定固定高度 */
    border-radius: 3px;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
    margin-right: 4px;
    text-decoration: none !important; /* 强制移除下划线 */
}

.btn-primary {
    color: #fff;
    background-color: #1976d2;
    border-color: #1976d2;
}

.btn-primary:hover {
    background-color: #1565c0;
    border-color: #1565c0;
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
    text-decoration: none !important; /* 强制移除下划线 */
}

.btn-success:hover {
    background-color: #218838;
    border-color: #218838;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #c82333;
}

/* 按钮容器样式 */
.buttons-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 6px; /* 减小间距 */
}

/* 两列布局表单样式 */
.form-group.horizontal {
    display: flex;
    align-items: center;
}

.form-label {
    width: 70px;
    text-align: right;
    padding-right: 8px;
    flex-shrink: 0;
    font-size: 11px;
    font-weight: 500;
    color: #555;
    margin-bottom: 0;
}

.form-input {
    flex: 1;
}

.form-section {
    margin-bottom: 0px;
}

.form-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 5px;
    padding-bottom: 3px;
    border-bottom: 1px solid #eee;
    color: #333;
}

.radio-group {
    display: flex;
    align-items: center;
}

.form-control {
    display: block;
    width: 100%;
    padding: 3px 5px; /* 进一步减小内边距 */
    font-size: 12px; /* 减小字体大小 */
    line-height: 1.3;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 3px;
    transition: border-color 0.15s ease-in-out;
    height: 22px; /* 进一步减小高度 */
    box-sizing: border-box;
}

textarea.form-control {
    height: auto;
    min-height: 22px;
    line-height: 1.2;
    padding-top: 2px;
    padding-bottom: 2px;
}

.form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

select.form-control {
    height: 22px; /* 进一步减小高度 */
    padding: 0 5px; /* 调整内边距 */
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 8px 8px;
    padding-right: 1.5rem;
}

label {
    display: inline-block;
    margin-bottom: 2px; /* 减小间距 */
    font-weight: 500;
    font-size: 12px; /* 减小字体大小 */
    color: #555;
}

/* 页面标题 */
.page-header {
    margin-bottom: 10px; /* 减小间距 */
    padding-bottom: 6px; /* 减小内边距 */
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h1 {
    margin: 0;
    font-size: 16px; /* 减小字体大小 */
    font-weight: 600;
    color: #333;
}

/* 卡片样式 */
.card {
    background-color: white;
    border-radius: 3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    padding: 8px; /* 减小内边距 */
    margin-bottom: 10px; /* 减小间距 */
}

.card-title {
    margin-top: 0;
    margin-bottom: 8px; /* 减小间距 */
    font-size: 14px; /* 减小字体大小 */
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px; /* 减小内边距 */
}

/* 网格系统 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -5px; /* 减小间距 */
    margin-left: -5px; /* 减小间距 */
}

.col {
    flex: 1;
    padding-right: 5px; /* 减小内边距 */
    padding-left: 5px; /* 减小内边距 */
    min-width: 0;
}


/* 模态框样式 - 修复点击问题 */
.modal {
    display: none;
    position: fixed;
    z-index: 200; /* 适中的z-index */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    pointer-events: none; /* 隐藏时不阻止点击 */
}

.modal.show {
    pointer-events: auto; /* 显示时恢复点击 */
}.modal-content {
    background-color: #fefefe;
    margin: 8% auto;
    padding: 10px; /* 减小内边距 */
    border: 1px solid #ddd;
    border-radius: 3px;
    width: 80%;
    max-width: 520px; /* 减小最大宽度 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.close {
    color: #aaa;
    float: right;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: black;
}

.record-details p {
    margin: 3px 0; /* 减小间距 */
}

/* 紧凑型表格 */
.table-sm th, .table-sm td {
    padding: 3px 5px;
    font-size: 11px;
}

/* 紧凑型表单 */
.form-control-sm {
    height: 20px;
    padding: 1px 4px;
    font-size: 11px;
}

/* 图片上传区域 */
.image-upload-container {
    margin-top: 2px;
}

.image-preview-area {
    min-height: 20px;
    margin-bottom: 3px;
}

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: center;
}

.input-group-append {
    margin-left: 3px;
}

.input-group-append .btn {
    padding: 1px 5px;
    font-size: 11px;
    height: 22px;
}

/* 单选框和复选框样式 */
.form-check-inline {
    margin-right: 8px;
    display: inline-flex;
    align-items: center;
}

.form-check-input {
    margin-top: 0;
    margin-right: 2px;
}

.form-check-label {
    font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar {
        position: relative;
    }
    
    .navbar-left {
        display: flex !important;
        align-items: center !important;
        width: auto;
        justify-content: flex-start !important;
        flex: 1 !important;
    }
    
    .navbar-nav {
        display: flex !important;
        width: auto;
        margin-top: 0 !important;
        margin-right: 10px !important;
        float: left !important;
    }
    
    .nav-item {
        width: auto;
    }
    
    .nav-link {
        width: auto;
        padding: 0 8px;
        text-align: left;
    }
    
    /* 移动端下拉菜单从左侧滑出 */
    .dropdown-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 85%;
        height: 100vh;
        max-height: 100vh;
        background-color: white;
        border-radius: 0 8px 8px 0;
        box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
        padding: 0;
        margin: 0;
        border: none;
        z-index: 50;
        transition: left 0.35s cubic-bezier(0.16, 1, 0.3, 1); /* 优化过渡效果 */
        overflow-y: auto;
        will-change: transform; /* 提示浏览器优化动画性能 */
        transform: translateZ(0); /* 强制硬件加速 */
    }
    
    /* 显示下拉菜单时的状态 */
    .dropdown-menu.show {
        left: 0;
    }
    
    /* 添加遮罩层 - 移除灰色背景 */
    .dropdown-menu.show::before {
        display: none; /* 完全移除遮罩层 */
    }
    
    /* 移动端下拉菜单内部样式 */
    .dropdown-menu .menu-grid {
        width: 100%;
        display: block;
    }
    
    .dropdown-menu .menu-row {
        display: block;
    }
    
    .dropdown-menu .menu-cell {
        display: block;
        width: 100% !important; /* 覆盖桌面版的宽度设置 */
        margin-left: 0 !important; /* 移除负边距，避免移动端布局问题 */
    }
    
    .dropdown-menu.multi-column {
        width: 100%; /* 移动端宽度为100% */
        max-width: 320px; /* 但最大宽度限制为320px */
    }
    
    /* 添加关闭按钮 */
    .dropdown-menu::after {
        content: '×';
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        color: #6c757d;
        cursor: pointer;
    }
    
    /* 移除强制显示规则 */
    .nav-item:hover .dropdown-menu {
        display: none;
    }
    
    th, td {
        padding: 2px 4px;
    }
    
    .modal-content {
        width: 90%;
    }
    
    .col {
        flex: 0 0 100%;
        margin-bottom: 10px;
    }
    
    .form-group.horizontal {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .form-label {
        width: 100%;
        text-align: left;
        margin-bottom: 4px;
    }
    
    .form-input {
        width: 100%;
    }
    
    .menu-grid {
        display: block;
    }
    
    .menu-row {
        display: block;
        margin-bottom: 5px;
    }
    
    .menu-cell {
        display: block;
        border-right: none;
        border-bottom: 1px dashed #ccc;
        padding: 8px 5px; /* 减少水平内边距，与桌面版一致 */
        width: 100% !important; /* 覆盖桌面版的宽度设置 */
    }
    
    .menu-cell:first-child {
        padding-right: 8px; /* 在移动视图中保持与桌面版一致的内边距 */
    }

    .menu-cell:nth-child(2) {
        padding-left: 8px; /* 在移动视图中与桌面视图保持一致 */
        padding-right: 8px; /* 增加右侧内边距 */
        text-align: center; /* 文本居中对齐 */
    }

    .menu-cell:nth-child(3) {
        padding-left: 8px; /* 第三列左侧内边距 */
        padding-right: 8px; /* 第三列右侧内边距 */
        text-align: center; /* 文本居中对齐 */
    }

    .menu-cell:last-child {
        border-bottom: none;
        padding-left: 8px; /* 增加左侧内边距 */
        text-align: center; /* 文本居中对齐 */
        margin-left: 0; /* 移动端不需要负边距 */
    }
} 

/* 响应式设计 */
@media (max-width: 576px) {
    .floating-panel {
        width: 85%; /* 移动端宽度更大一点 */
        z-index: 100; /* 提高层级 */
        padding-bottom: 20px; /* 增加底部内边距 */
    }
    
    /* 移动端面板样式增强 */
    .panel-header {
        padding: 15px 10px;
        height: 50px;
        display: flex;
        align-items: center;
    }
    
    .panel-body {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* 增加iOS滑动惯性 */
    }
    
    .float-card {
        width: 100%;
    }
    
    .summary-stats {
        flex-direction: column;
    }
    
    .stat-card {
        min-width: 100%;
    }
    
    /* 小屏幕导航栏样式 */
    .navbar .container {
        padding: 0 4px;
        margin: 0 !important;
        max-width: 100% !important;
    }
    
    .navbar-left {
        flex: 1;
    }
    
    .navbar-brand {
        margin-right: 5px;
    }
    
    .navbar-nav {
        margin-right: 5px;
    }
    
    .nav-link {
        padding: 0 5px;
        font-size: 11px;
    }
    
    .quick-search {
        margin-right: 3px;
    }
    
    #quick-search-input {
        width: 60px;
        font-size: 10px;
    }
} 

/* 检验记录样式 */
.inspection-records-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.records-header h4 {
    margin: 0;
    font-size: 14px;
    color: #495057;
}

.records-filter {
    width: 120px;
}

.records-list {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    min-height: 300px;
}

.records-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

#records-page-info {
    font-size: 12px;
    color: #6c757d;
}

.loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    color: #6c757d;
    font-size: 14px;
} 

.dropdown-menu .menu-grid {
    width: 100%;
    display: table;
    box-shadow: none;
    border-radius: 0;
}

.dropdown-menu .menu-row {
    background-color: transparent;
    display: table-row;
}

.dropdown-menu .menu-cell {
    padding: 6px 5px; /* 减少水平内边距，与主视图一致 */
    display: table-cell;
}

.dropdown-menu .menu-cell:first-child {
    width: 25% !important; /* 强制第一列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    padding-right: 8px; /* 与主视图保持一致的内边距 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.dropdown-menu .menu-cell:nth-child(2) {
    width: 25% !important; /* 强制第二列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    padding-left: 8px; /* 与主视图保持一致 */
    padding-right: 8px; /* 增加右侧内边距 */
    text-align: center; /* 文本居中对齐 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.dropdown-menu .menu-cell:nth-child(3) {
    width: 25% !important; /* 强制第三列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    padding-left: 8px; /* 左侧内边距 */
    padding-right: 8px; /* 右侧内边距 */
    text-align: center; /* 文本居中对齐 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.dropdown-menu .menu-cell:last-child {
    width: 25% !important; /* 强制第四列宽度为25% */
    max-width: 25% !important; /* 限制最大宽度 */
    min-width: 25% !important; /* 限制最小宽度 */
    padding-left: 8px; /* 增加左侧内边距 */
    text-align: center; /* 文本居中对齐 */
    margin-left: 0; /* 移除负边距 */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.dropdown-menu .menu-item {
    font-size: 12px;
    color: #333;
}

/* 下拉菜单标题头部 */
.dropdown-header {
    padding: 15px;
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    display: none; /* 默认隐藏，仅在移动端显示 */
    justify-content: center;
    align-items: center;
    text-align: center;
}

.dropdown-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
    font-family: "黑体", "SimHei", sans-serif;
    color: #000;
}

/* 面板标题头部 */
.panel-header {
    padding: 15px;
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    text-align: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
    font-family: "黑体", "SimHei", sans-serif;
    color: #000;
    width: auto;
    position: static;
}

.close-panel {
    font-size: 20px;
    cursor: pointer;
    color: #6c757d;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
}

.close-panel:hover {
    color: #343a40;
}

@media (max-width: 768px) {
    .dropdown-header {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 15px;
        height: 50px;
    }
}

/* 多功能按钮样式 */
.multi-function-btn {
    position: relative;
    display: inline-block;
}

.function-dropdown {
    position: fixed; /* 改为fixed定位 */
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 160px;
    z-index: 1000;
    display: none;
    overflow: hidden;
    /* 防止下拉菜单导致页面出现滚动条 */
    max-height: calc(100vh - 50px);
    overflow-y: auto;
    /* 确保下拉菜单不会导致页面布局问题 */
    margin: 0;
    padding: 0;
}

/* 移除hover时自动显示下拉菜单的样式，改为通过JavaScript控制 */
/* .multi-function-btn:hover .function-dropdown {
    display: block;
} */

.function-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    font-size: 12px;
    transition: background-color 0.2s;
}

.function-item:hover {
    background-color: #f5f5f5;
    color: #1976d2;
}

.function-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    color: #1976d2;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .function-dropdown {
        width: 140px;
        right: -5px;
    }
    
    .function-item {
        padding: 6px 10px;
        font-size: 11px;
    }
} 

/* 移动端响应式样式 */
@media (max-width: 768px) {
    /* 表单元素样式统一 */
    input[type="text"], 
    input[type="date"], 
    input[type="number"], 
    input[type="password"],
    input[type="email"],
    select, 
    textarea,
    .form-control,
    .quick-search-input,
    .date-range-display {
        height: 38px !important;
        padding: 2px 8px;
        font-size: 13px !important;
        box-sizing: border-box;
    }
    
    /* 按钮样式 */
    .btn {
        height: 38px !important;
        line-height: 36px !important;
        font-size: 13px !important;
    }
    
    /* 搜索表单 */
    .quick-search-form {
        height: 38px !important;
    }
    
    /* 移动端按钮宽度优化 */
    #advanced-search-btn, 
    .header-right .btn-success {
        width: auto !important; /* 移除固定宽度 */
        min-width: 80px !important; /* 设置最小宽度 */
        padding: 0 8px !important; /* 减小内边距 */
    }
    
    /* 移动端搜索框宽度优化 */
    .quick-search-form {
        width: 60% !important; /* 减小宽度 */
        margin-right: 0 !important; /* 移除右侧间距 */
    }
    
    /* 移动端按钮布局优化 */
    .header-right {
        display: flex;
        flex-wrap: wrap !important; /* 允许换行 */
        width: 100%;
        align-items: center;
    }
    
    /* 移动端搜索和按钮行 */
    .header-right > .quick-search-form,
    .header-right > .buttons-container {
        display: inline-flex;
    }
    
    /* 搜索和按钮容器在一行 */
    .header-right > .date-filters-container {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .header-right > .quick-search-form + .buttons-container {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        flex: 1;
    }
    
    /* 按钮容器 */
    .buttons-container {
        display: flex;
        gap: 4px !important;
        margin-left: 4px;
        flex-shrink: 0; /* 防止按钮被压缩 */
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    /* 进一步减小按钮大小 */
    #advanced-search-btn, 
    .header-right .btn-success {
        min-width: 70px !important;
        font-size: 12px !important;
        padding: 0 5px !important;
    }
    
    /* 调整搜索框和按钮的比例 */
    .quick-search-form {
        width: 55% !important;
    }
    
    /* 减小按钮间距 */
    .buttons-container {
        gap: 2px !important;
    }
} 

/* 分页容器样式 */
.pagination-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    padding: 5px 0;
    font-size: 12px;
}

.summary {
    color: #666;
    white-space: nowrap;
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.pagination a, .pagination span {
    padding: 2px 5px;
    text-decoration: none;
    border: 1px solid #ddd;
    margin: 0 2px;
    color: #333;
    font-size: 11px;
    display: inline-block;
}

.pagination a:hover {
    background-color: #f1f1f1;
}

.pagination .active {
    background-color: #1976d2;
    color: white;
    border: 1px solid #1976d2;
}

.pagination .disabled {
    color: #aaa;
    border: 1px solid #ddd;
}

/* 新增的分页导航按钮样式 */
.current-page-display {
    padding: 2px 10px !important;
    background-color: #f8f9fa;
    color: #333;
    font-weight: bold;
    border: 1px solid #ddd;
    margin: 0 5px !important;
    display: inline-block;
    text-align: center;
    min-width: 70px;
}

.page-nav-btn {
    min-width: 50px;
    text-align: center;
    font-weight: normal;
    display: inline-block !important;
}

.prev-page-btn, .next-page-btn {
    background-color: #f0f0f0;
}

.prev-page-btn:hover, .next-page-btn:hover {
    background-color: #e0e0e0;
}

.page-size-selector {
    font-size: 11px;
    color: #666;
    display: flex;
    align-items: center;
    white-space: nowrap;
    min-width: 130px;
}

.page-size-selector select {
    padding: 2px 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 11px;
    margin-left: 5px;
    cursor: pointer;
    width: 65px;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 8px;
    }
    
    .summary {
        text-align: center;
        width: 100%;
    }
    
    .pagination {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 5px 0;
    }
    
    .pagination a, .pagination span {
        margin: 2px;
    }
}

/* 关键尺寸测量区域 */
#dimension-measurement-section {
    margin: 0;
    padding: 0;
    border: none;
}

/* 关键尺寸测量样式 */
.dimension-measurement-container {
    margin: 0 0 12px 0;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 2px;
    background-color: #f9f9f9;
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
    padding-bottom: 2px;
    border-bottom: 1px solid #ddd;
}

.dimension-header h3 {
    margin: 0;
    color: #333;
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.dimension-header h3 i {
    color: #007bff;
    font-size: 12px;
}

.dimension-controls {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.dimension-controls .btn {
    font-size: 11px;
    padding: 2px 4px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2px;
    height: 22px;
}

.dimension-controls .btn i {
    font-size: 10px;
}

.dimension-controls .btn-success {
    background-color: #28a745;
    color: white;
}

.dimension-controls .btn-success:hover {
    background-color: #218838;
}

.dimension-controls .btn-info {
    background-color: #17a2b8;
    color: white;
}

.dimension-controls .btn-info:hover {
    background-color: #138496;
}

.dimension-controls .btn-primary {
    background-color: #007bff;
    color: white;
}

.dimension-controls .btn-primary:hover {
    background-color: #0056b3;
}

.dimension-controls .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.dimension-controls .btn-secondary:hover {
    background-color: #545b62;
}

.dimension-controls .btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.dimension-controls .btn-warning:hover {
    background-color: #e0a800;
}

.dimension-table-container {
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 0px;
}

.dimension-table {
    margin: 0;
    font-size: 11px;
    background-color: white;
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.dimension-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    padding: 2px 1px;
    border: 1px solid #dee2e6;
    white-space: normal;
    font-size: 11px;
    height: 26px;
    line-height: 1.1;
}

/* 确保表格第一行没有额外的上边框 */
.dimension-table thead tr:first-child th {
    border-top: 1px solid #dee2e6;
}

.dimension-table tbody tr:first-child td {
    border-top: 1px solid #dee2e6;
}

/* 表头中的单位样式 */
.dimension-table th .unit {
    font-size: 9px;
    color: #666;
    font-weight: 400;
    display: block;
}

.dimension-table td {
    padding: 0;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    position: relative;
    height: 20px;
    min-height: 20px;
    line-height: 1;
    text-align: center;
}

.dimension-table .form-control-sm {
    font-size: 14px;
    text-align: center !important;
    padding: 0;
    margin: 0;
    height: 100%;
    width: 100%;
    min-height: 100%;
    border: none;
    background: transparent;
    box-shadow: none;
    border-radius: 0;
    display: block;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    line-height: 1;
}

.dimension-table .form-control-sm:focus {
    border: 1px solid #007bff;
    background: #fff;
    box-shadow: none;
    outline: none;
    padding: 0px;
}

/* 所有输入框完全占满单元格 */
.dimension-table input[type="number"] {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    text-align: center !important;
    padding: 0;
    margin: 0;
    border: none;
    background: transparent;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 1;
    -moz-appearance: textfield; /* Firefox */
}

/* 取消Chrome/Safari的数值输入框箭头 */
.dimension-table input[type="number"]::-webkit-outer-spin-button,
.dimension-table input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.dimension-table input[type="number"]:focus {
    padding: 0px;
    border: 1px solid #007bff;
    background: #fff;
    text-align: center !important;
}

/* 特定输入框类型的居中样式 */
.dimension-table .dimension-input,
.dimension-table .upper-tolerance-input,
.dimension-table .lower-tolerance-input,
.dimension-table .measured-input {
    text-align: center !important;
}

/* 位置名称输入框 */
.dimension-table .position-input {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    text-align: center !important;
    padding: 0;
    margin: 0;
    border: none;
    background: transparent;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 1;
}

.dimension-table .position-input:focus {
    padding: 0px;
    border: 1px solid #007bff;
    background: #fff;
}

/* 备注输入框 */
.dimension-table .remarks-input {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    text-align: center !important;
    resize: none;
    padding: 0;
    margin: 0;
    border: none;
    background: transparent;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 1;
}

.dimension-table .remarks-input:focus {
    padding: 0px;
    border: 1px solid #007bff;
    background: #fff;
}

/* 结果选择框 */
.dimension-table .result-select {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    border: none;
    background: transparent;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
}

.dimension-table .result-select:focus {
    padding: 0px;
    border: 1px solid #007bff;
    background: #fff;
}

/* 结果选择框背景色 */
.dimension-table .result-select option[value="合格"] {
    background-color: #d4edda;
    color: #155724;
}

.dimension-table .result-select option[value="不合格"] {
    background-color: #f8d7da;
    color: #721c24;
}

.dimension-table .result-select option[value="待定"] {
    background-color: #fff3cd;
    color: #856404;
}

.dimension-table .result-select option[value="AOD"] {
    background-color: #fff3cd;
    color: #856404;
}

/* 根据选中值设置选择框背景色 */
.dimension-table .result-select[data-value="合格"] {
    background-color: #d4edda !important;
    color: #155724;
}

.dimension-table .result-select[data-value="不合格"] {
    background-color: #f8d7da !important;
    color: #721c24;
}

.dimension-table .result-select[data-value="待定"] {
    background-color: #fff3cd !important;
    color: #856404;
}

.dimension-table .result-select[data-value="AOD"] {
    background-color: #fff3cd !important;
    color: #856404;
}

/* 删除按钮样式 */
.dimension-table .remove-row {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    font-size: 12px;
    border: none;
    background: #dc3545;
    color: white;
    border-radius: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dimension-table .remove-row:hover {
    background: #c82333;
}

.dimension-table input[type="number"] {
    text-align: right;
}

.dimension-range {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    font-size: 12px;
    color: #666;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0;
    background-color: transparent;
    border-radius: 0;
    word-break: break-all;
    line-height: 1;
    margin: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

.measured-input {
    position: relative;
}

.measured-input[title]:hover {
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.result-select {
    font-size: 10px !important;
}

.result-select option[value="合格"] {
    color: #28a745;
}

.result-select option[value="不合格"] {
    color: #dc3545;
}

.result-select option[value="待定"] {
    color: #ffc107;
}

/* 历史数据提示样式 */
.measured-input[title]:not([title=""]):before {
    content: "📊";
    position: absolute;
    right: 2px;
    top: 2px;
    font-size: 10px;
    opacity: 0.6;
}

/* 移动端关键尺寸测量样式 */
@media (max-width: 768px) {
    .dimension-measurement-container {
        margin: 0 0 8px 0;
        padding: 2px;
    }

    .dimension-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        margin-bottom: 2px;
        padding-bottom: 2px;
    }

    .dimension-header h3 {
        font-size: 12px;
    }

    .dimension-header h3 i {
        font-size: 11px;
    }

    .dimension-controls {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 4px;
    }

    .dimension-controls .btn {
        font-size: 9px;
        padding: 2px 4px;
    }

    .dimension-table-container {
        max-height: 300px;
        margin-top: 0px;
    }

    .dimension-table {
        font-size: 9px;
    }

    .dimension-table th {
        padding: 1px;
        font-size: 10px;
        height: 24px;
        line-height: 1.1;
        white-space: normal;
    }

    .dimension-table th .unit {
        font-size: 8px;
    }

    .dimension-table td {
        padding: 0;
        height: 18px;
        min-height: 18px;
        line-height: 1;
    }

    .dimension-table .form-control-sm {
        font-size: 13px;
        padding: 0px;
        text-align: center !important;
    }

    .dimension-table input[type="number"] {
        padding: 0px;
        font-size: 13px;
        text-align: center !important;
    }

    .dimension-table input[type="number"]:focus {
        padding: 0px;
        border: 1px solid #007bff;
        text-align: center !important;
    }

    .dimension-table .position-input {
        padding: 0px;
        font-size: 13px;
        text-align: center !important;
    }

    .dimension-table .position-input:focus {
        padding: 0px;
        border: 1px solid #007bff;
        text-align: center !important;
    }

    .dimension-table .remarks-input {
        padding: 0px;
        font-size: 13px;
        text-align: center !important;
    }

    .dimension-table .remarks-input:focus {
        padding: 0px;
        border: 1px solid #007bff;
        text-align: center !important;
    }

    .dimension-table .result-select {
        padding: 0px;
        font-size: 14px;
        font-weight: 600;
    }

    .dimension-table .result-select:focus {
        padding: 0px;
        border: 1px solid #007bff;
    }

    .dimension-table .remove-row {
        font-size: 10px;
    }

    .dimension-range {
        font-size: 10px;
        padding: 2px;
    }

    .dimension-range {
        font-size: 8px;
        padding: 1px;
    }
}

/* 不良问题记录区域样式 */
.issues-section {
    background: #fff;
    border-radius: 6px;
    padding: 2px;
    border: 1px solid #e0e0e0;
    margin: 0 0 12px 0;
    background-color: #f9f9f9;
}

.issues-section-title {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
    padding-bottom: 2px;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.issues-section-title .title-left {
    display: flex;
    align-items: center;
    gap: 6px;
}

.issues-section-title .title-left i {
    color: #007bff;
    font-size: 12px;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 4px;
}

.button-group .btn-sm {
    font-size: 11px;
    padding: 2px 4px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2px;
    height: 22px;
}

.button-group .btn-sm i {
    font-size: 10px;
}

/* 移动端不良问题记录样式 */
@media (max-width: 768px) {
    .issues-section {
        margin: 0 0 8px 0;
        padding: 2px;
    }

    .issues-section-title {
        font-size: 12px;
        margin-bottom: 2px;
        padding-bottom: 2px;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .issues-section-title .title-left {
        gap: 4px;
    }

    .issues-section-title .title-left i {
        font-size: 11px;
    }

    .button-group {
        gap: 4px;
        align-self: flex-end;
    }

    .button-group .btn-sm {
        font-size: 9px;
        padding: 2px 4px;
    }

    .button-group .btn-sm i {
        font-size: 9px;
    }
}

/* 不良问题记录表格样式 */
.issues-section .table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0px;
    background: white;
    border-radius: 4px;
    overflow: hidden;
    font-size: 12px;
}

.issues-section .table th {
    background: #f8f9fa;
    padding: 2px 1px;
    font-size: 13px;
    font-weight: 600;
    color: #333;
    border: 1px solid #dee2e6;
    text-align: center;
    height: 16px;
    line-height: 1.1;
}

.issues-section .table td {
    padding: 0px;
    font-size: 14px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
    height: 12px !important;
    min-height: 12px !important;
    line-height: 1;
    text-align: center;
    position: relative;
}

.issues-section .table tbody tr:hover {
    background-color: #f9f9f9;
}

/* 不良问题记录表格输入框样式 */
.issues-section .table input,
.issues-section .table select,
.issues-section .table textarea {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    border: none;
    background: transparent;
    padding: 0;
    margin: 0;
    font-size: 13px;
    text-align: center;
    box-sizing: border-box;
    line-height: 1;
}

.issues-section .table input:focus,
.issues-section .table select:focus,
.issues-section .table textarea:focus {
    border: 1px solid #007bff;
    background: #fff;
    outline: none;
}

/* 移动端不良问题记录表格样式 */
@media (max-width: 768px) {
    .issues-section .table th {
        padding: 1px;
        font-size: 12px;
        height: 14px;
    }

    .issues-section .table td {
        padding: 0;
        height: 12px !important;
        min-height: 12px !important;
    }

    .issues-section .table input,
    .issues-section .table select,
    .issues-section .table textarea {
        font-size: 12px;
    }
}

/* 图片上传容器样式 */
.image-upload-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: flex-start;
    padding: 2px;
}

.image-preview-item {
    position: relative;
    width: 60px;
    height: 60px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    background: #f8f9fa;
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-remove-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 16px;
    height: 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.image-remove-btn:hover {
    background: #c82333;
}

.image-add-btn {
    width: 60px;
    height: 60px;
    border: 2px dashed #007bff;
    border-radius: 4px;
    background: transparent;
    color: #007bff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    transition: all 0.3s ease;
}

.image-add-btn:hover {
    background: #f8f9ff;
    border-color: #0056b3;
    color: #0056b3;
}

.image-add-btn input[type="file"] {
    display: none;
}



/* 移动端图片上传样式 */
@media (max-width: 768px) {
    .image-preview-item {
        width: 50px;
        height: 50px;
    }

    .image-add-btn {
        width: 50px;
        height: 50px;
        font-size: 16px;
    }

    .image-remove-btn {
        width: 14px;
        height: 14px;
        font-size: 8px;
    }
}

/* 首页特定样式 - 自适应高度 */
.container .row {
    min-height: calc(100vh - 36px - 60px); /* 减去导航栏和页面标题的高度 */
    align-items: stretch;
}

.container .row .col {
    display: flex;
    flex-direction: column;
}

.container .row .col .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 200px;
}

/* 按钮点击修复 */
.btn, .test-button, button {
    position: relative;
    z-index: 10;
    pointer-events: auto;
}

.nav-link, .dropdown-item, .menu-item {
    position: relative;
    z-index: 10;
    pointer-events: auto;
}